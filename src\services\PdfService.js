import { PDF_URLS, getPdfUrl, isPdfAvailable } from '../config/pdfConfig';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

class PdfService {
  constructor() {
    this.cache = new Map();
    this.downloadQueue = new Set();
  }

  /**
   * Get PDF URL for a specific subject and type
   */
  getPdfUrl(subject, type, chapter = null) {
    return getPdfUrl(subject, type, chapter);
  }

  /**
   * Check if PDF is available
   */
  isPdfAvailable(subject, type, chapter = null) {
    return isPdfAvailable(subject, type, chapter);
  }

  /**
   * Open PDF in the device's default PDF viewer
   */
  async openPdf(subject, type, chapter = null) {
    try {
      const url = this.getPdfUrl(subject, type, chapter);

      if (!url) {
        Alert.alert('Error', 'PDF not available for this subject/chapter');
        return false;
      }

      // Validate URL accessibility
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (fetchError) {
        console.error('PDF URL not accessible:', fetchError);
        Alert.alert(
          'PDF Access Error',
          'The PDF file is not accessible. Please check your internet connection and try again.'
        );
        return false;
      }

      // Check if we have a cached version
      const cachedPath = await this.getCachedPdfPath(subject, type, chapter);

      if (cachedPath && await FileSystem.getInfoAsync(cachedPath).then(info => info.exists)) {
        // Open cached version
        await this.openPdfFile(cachedPath);
        return true;
      }

      // Open remote URL directly
      await this.openPdfFile(url);
      return true;

    } catch (error) {
      console.error('Error opening PDF:', error);
      Alert.alert('Error', `Failed to open PDF: ${error.message}`);
      return false;
    }
  }

  /**
   * Download PDF for offline viewing
   */
  async downloadPdf(subject, type, chapter = null, onProgress = null) {
    try {
      const url = this.getPdfUrl(subject, type, chapter);
      const filename = this.getPdfFilename(subject, type, chapter);
      
      if (!url) {
        throw new Error('PDF URL not available');
      }

      if (this.downloadQueue.has(filename)) {
        console.log('PDF already downloading:', filename);
        return false;
      }

      this.downloadQueue.add(filename);

      const localPath = `${FileSystem.documentDirectory}pdfs/${filename}`;
      
      // Ensure directory exists
      await FileSystem.makeDirectoryAsync(`${FileSystem.documentDirectory}pdfs/`, { 
        intermediates: true 
      });

      // Download with progress tracking
      const downloadResumable = FileSystem.createDownloadResumable(
        url,
        localPath,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          if (onProgress) {
            onProgress(progress);
          }
        }
      );

      const result = await downloadResumable.downloadAsync();
      
      if (result && result.uri) {
        this.cache.set(filename, result.uri);
        console.log('PDF downloaded successfully:', filename);
        return result.uri;
      }

      throw new Error('Download failed');

    } catch (error) {
      console.error('Error downloading PDF:', error);
      throw error;
    } finally {
      const filename = this.getPdfFilename(subject, type, chapter);
      this.downloadQueue.delete(filename);
    }
  }

  /**
   * Get cached PDF path
   */
  async getCachedPdfPath(subject, type, chapter = null) {
    const filename = this.getPdfFilename(subject, type, chapter);
    
    if (this.cache.has(filename)) {
      return this.cache.get(filename);
    }

    const localPath = `${FileSystem.documentDirectory}pdfs/${filename}`;
    const fileInfo = await FileSystem.getInfoAsync(localPath);
    
    if (fileInfo.exists) {
      this.cache.set(filename, localPath);
      return localPath;
    }

    return null;
  }

  /**
   * Check if PDF is cached locally
   */
  async isPdfCached(subject, type, chapter = null) {
    const cachedPath = await this.getCachedPdfPath(subject, type, chapter);
    return cachedPath !== null;
  }

  /**
   * Get PDF filename
   */
  getPdfFilename(subject, type, chapter = null) {
    if (chapter) {
      return `${subject}_${type}_chapter${chapter}.pdf`;
    }
    return `${subject}_${type}.pdf`;
  }

  /**
   * Open PDF file using the system's default PDF viewer
   */
  async openPdfFile(uri) {
    try {
      // For React Native, you might want to use a PDF viewer library
      // like react-native-pdf or expo-document-picker
      
      // For now, we'll use Linking to open in external app
      const { Linking } = require('react-native');
      
      const canOpen = await Linking.canOpenURL(uri);
      if (canOpen) {
        await Linking.openURL(uri);
      } else {
        throw new Error('Cannot open PDF');
      }
    } catch (error) {
      console.error('Error opening PDF file:', error);
      throw error;
    }
  }

  /**
   * Get all available PDFs for a subject
   */
  getAvailablePdfs(subject) {
    const subjectPdfs = PDF_URLS[subject];
    if (!subjectPdfs) return [];

    const pdfs = [];
    
    Object.keys(subjectPdfs).forEach(type => {
      if (typeof subjectPdfs[type] === 'string') {
        pdfs.push({ subject, type, title: `${subject} ${type}` });
      } else if (typeof subjectPdfs[type] === 'object') {
        Object.keys(subjectPdfs[type]).forEach(chapter => {
          pdfs.push({ 
            subject, 
            type, 
            chapter: chapter.replace('chapter', ''),
            title: `${subject} ${type} Chapter ${chapter.replace('chapter', '')}`
          });
        });
      }
    });

    return pdfs;
  }

  /**
   * Clear PDF cache
   */
  async clearCache() {
    try {
      const cacheDir = `${FileSystem.documentDirectory}pdfs/`;
      const dirInfo = await FileSystem.getInfoAsync(cacheDir);
      
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(cacheDir);
      }
      
      this.cache.clear();
      console.log('PDF cache cleared');
    } catch (error) {
      console.error('Error clearing PDF cache:', error);
    }
  }

  /**
   * Get cache size
   */
  async getCacheSize() {
    try {
      const cacheDir = `${FileSystem.documentDirectory}pdfs/`;
      const dirInfo = await FileSystem.getInfoAsync(cacheDir);
      
      if (!dirInfo.exists) return 0;

      const files = await FileSystem.readDirectoryAsync(cacheDir);
      let totalSize = 0;

      for (const file of files) {
        const fileInfo = await FileSystem.getInfoAsync(`${cacheDir}${file}`);
        totalSize += fileInfo.size || 0;
      }

      return totalSize;
    } catch (error) {
      console.error('Error getting cache size:', error);
      return 0;
    }
  }
}

export default new PdfService();
