{"buildFiles": ["C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\.cxx\\Debug\\5655k3p1\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\.cxx\\Debug\\5655k3p1\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_RNCSlider.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_rnpicker.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNCSlider", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNGoogleMobileAdsSpec"}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNHapticFeedbackSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rnclipboard::@6385240493dfcaf22ab7": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnclipboard"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnpdf::@7ef4ba29a735fbf48be7": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnpdf"}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnpicker", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\Debug\\5655k3p1\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37f986109817451798d14c7952a3b6e1\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}