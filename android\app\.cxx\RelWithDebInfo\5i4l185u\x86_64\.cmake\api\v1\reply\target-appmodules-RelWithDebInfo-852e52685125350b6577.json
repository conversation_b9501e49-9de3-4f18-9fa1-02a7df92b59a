{"artifacts": [{"path": "C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/x86_64/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["C:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 83, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 77, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 82, "parent": 12}, {"file": 5}, {"command": 4, "file": 5, "line": 80, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni"}, {"backtrace": 11, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni"}, {"backtrace": 13, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 15, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a"}, {"backtrace": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_rnclipboard::@6385240493dfcaf22ab7"}, {"backtrace": 4, "id": "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnpdf::@7ef4ba29a735fbf48be7"}, {"backtrace": 4, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c"}, {"backtrace": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\x86_64\\libreact_codegen_RNCSlider.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\x86_64\\libreact_codegen_rnpicker.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\x86_64\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}