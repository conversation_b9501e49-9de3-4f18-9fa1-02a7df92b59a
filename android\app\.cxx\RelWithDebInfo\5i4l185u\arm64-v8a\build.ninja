# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCSlider cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rnclipboard cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnpdf cmake_object_order_depends_target_react_codegen_rnpicker cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/C_/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\C_\Users\gokul\quiz-bee-techs\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\C_\Users\gokul\quiz-bee-techs\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libappmodules.so

build C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_RelWithDebInfo rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o CMakeFiles/appmodules.dir/C_/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec RNHapticFeedbackSpec_autolinked_build/react_codegen_RNHapticFeedbackSpec RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rnclipboard_autolinked_build/react_codegen_rnclipboard rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnpdf_autolinked_build/react_codegen_rnpdf rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so  C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so  C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so  C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnclipboard


#############################################
# Order-only phony target for react_codegen_rnclipboard

build cmake_object_order_depends_target_react_codegen_rnclipboard: phony || rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/Props.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/States.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard\rnclipboardJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\react\renderer\components\rnclipboard

build rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o: CXX_COMPILER__react_codegen_rnclipboard_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/rnclipboard-generated.cpp || cmake_object_order_depends_target_react_codegen_rnclipboard
  DEP_FILE = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir\rnclipboard-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir
  OBJECT_FILE_DIR = rnclipboard_autolinked_build\CMakeFiles\react_codegen_rnclipboard.dir



#############################################
# Object library react_codegen_rnclipboard

build rnclipboard_autolinked_build/react_codegen_rnclipboard: phony rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnclipboard_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnclipboard_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnclipboard_autolinked_build/edit_cache: phony rnclipboard_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnclipboard_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnclipboard_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnclipboard_autolinked_build/rebuild_cache: phony rnclipboard_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Order-only phony target for react_codegen_RNCSlider

build cmake_object_order_depends_target_react_codegen_RNCSlider: phony || RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/db5badcd17e3d6b3dc2e1b877991a66a/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\db5badcd17e3d6b3dc2e1b877991a66a\renderer\components\RNCSlider\RNCSliderMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\db5badcd17e3d6b3dc2e1b877991a66a\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/f1826b4e1b088b69a4da731804ec55fe/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\f1826b4e1b088b69a4da731804ec55fe\cpp\react\renderer\components\RNCSlider\RNCSliderShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\f1826b4e1b088b69a4da731804ec55fe\cpp\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/120405363f2fd5f7c87dd454b0a2fc40/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\120405363f2fd5f7c87dd454b0a2fc40\android\build\generated\source\codegen\jni\RNCSlider-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\120405363f2fd5f7c87dd454b0a2fc40\android\build\generated\source\codegen\jni

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\47050d7efd95dbd227bb716bea95769f\jni\react\renderer\components\RNCSlider\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\47050d7efd95dbd227bb716bea95769f\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c68754e0d236c318d9d47d16f84798b0\codegen\jni\react\renderer\components\RNCSlider\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c68754e0d236c318d9d47d16f84798b0\codegen\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\a868b8d5466bc67761de466f3f5bec2a\source\codegen\jni\react\renderer\components\RNCSlider\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\a868b8d5466bc67761de466f3f5bec2a\source\codegen\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\47050d7efd95dbd227bb716bea95769f\jni\react\renderer\components\RNCSlider\RNCSliderJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\47050d7efd95dbd227bb716bea95769f\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c68754e0d236c318d9d47d16f84798b0\codegen\jni\react\renderer\components\RNCSlider\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c68754e0d236c318d9d47d16f84798b0\codegen\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\a868b8d5466bc67761de466f3f5bec2a\source\codegen\jni\react\renderer\components\RNCSlider\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\a868b8d5466bc67761de466f3f5bec2a\source\codegen\jni\react\renderer\components\RNCSlider


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Link the shared library C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_RNCSlider.so

build C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_RNCSlider_RelWithDebInfo RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/db5badcd17e3d6b3dc2e1b877991a66a/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/f1826b4e1b088b69a4da731804ec55fe/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/120405363f2fd5f7c87dd454b0a2fc40/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_RNCSlider.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_RNCSlider.so
  TARGET_PDB = react_codegen_RNCSlider.so.dbg


#############################################
# Utility command for edit_cache

build RNCSlider_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNCSlider_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCSlider_autolinked_build/edit_cache: phony RNCSlider_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNCSlider_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCSlider_autolinked_build/rebuild_cache: phony RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Order-only phony target for react_codegen_rnpicker

build cmake_object_order_depends_target_react_codegen_rnpicker: phony || rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDialogPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\RNCAndroidDropdownPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\rnpicker.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\f3e179e612d5365dd1c281c151a397be\jni\react\renderer\components\rnpicker\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\f3e179e612d5365dd1c281c151a397be\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\e64e4c8e457947268e1502aa0d45cea1\codegen\jni\react\renderer\components\rnpicker\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\e64e4c8e457947268e1502aa0d45cea1\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\db1a68c37668ee0b5b844736ce4c3d03\source\codegen\jni\react\renderer\components\rnpicker\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\db1a68c37668ee0b5b844736ce4c3d03\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\e64e4c8e457947268e1502aa0d45cea1\codegen\jni\react\renderer\components\rnpicker\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\e64e4c8e457947268e1502aa0d45cea1\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\db1a68c37668ee0b5b844736ce4c3d03\source\codegen\jni\react\renderer\components\rnpicker\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\db1a68c37668ee0b5b844736ce4c3d03\source\codegen\jni\react\renderer\components\rnpicker

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnpicker_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\f3e179e612d5365dd1c281c151a397be\jni\react\renderer\components\rnpicker\rnpickerJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir\f3e179e612d5365dd1c281c151a397be\jni\react\renderer\components\rnpicker


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Link the shared library C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_rnpicker.so

build C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnpicker_RelWithDebInfo rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnpicker_autolinked_build\CMakeFiles\react_codegen_rnpicker.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnpicker.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_rnpicker.so
  TARGET_PDB = react_codegen_rnpicker.so.dbg


#############################################
# Utility command for edit_cache

build rnpicker_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnpicker_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnpicker_autolinked_build/edit_cache: phony rnpicker_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnpicker_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnpicker_autolinked_build/rebuild_cache: phony rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNGoogleMobileAdsSpec


#############################################
# Order-only phony target for react_codegen_RNGoogleMobileAdsSpec

build cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec: phony || RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/RNGoogleMobileAdsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\RNGoogleMobileAdsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/RNGoogleMobileAdsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\2e26a5ddee9cdd09e5c46bc2607ebc87\RNGoogleMobileAdsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\2e26a5ddee9cdd09e5c46bc2607ebc87

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNGoogleMobileAdsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNGoogleMobileAdsSpec
  DEP_FILE = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir
  OBJECT_FILE_DIR = RNGoogleMobileAdsSpec_autolinked_build\CMakeFiles\react_codegen_RNGoogleMobileAdsSpec.dir\react\renderer\components\RNGoogleMobileAdsSpec



#############################################
# Object library react_codegen_RNGoogleMobileAdsSpec

build RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNGoogleMobileAdsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNGoogleMobileAdsSpec_autolinked_build/edit_cache: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNGoogleMobileAdsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNGoogleMobileAdsSpec_autolinked_build/rebuild_cache: phony RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNHapticFeedbackSpec


#############################################
# Order-only phony target for react_codegen_RNHapticFeedbackSpec

build cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec: phony || RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/RNHapticFeedbackSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\RNHapticFeedbackSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/RNHapticFeedbackSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\7f9d75c760005477a9a92acb25e45305\RNHapticFeedbackSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\7f9d75c760005477a9a92acb25e45305

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNHapticFeedbackSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNHapticFeedbackSpec
  DEP_FILE = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir
  OBJECT_FILE_DIR = RNHapticFeedbackSpec_autolinked_build\CMakeFiles\react_codegen_RNHapticFeedbackSpec.dir\react\renderer\components\RNHapticFeedbackSpec



#############################################
# Object library react_codegen_RNHapticFeedbackSpec

build RNHapticFeedbackSpec_autolinked_build/react_codegen_RNHapticFeedbackSpec: phony RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNHapticFeedbackSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNHapticFeedbackSpec_autolinked_build/edit_cache: phony RNHapticFeedbackSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNHapticFeedbackSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNHapticFeedbackSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNHapticFeedbackSpec_autolinked_build/rebuild_cache: phony RNHapticFeedbackSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnpdf


#############################################
# Order-only phony target for react_codegen_rnpdf

build cmake_object_order_depends_target_react_codegen_rnpdf: phony || rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/Props.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/States.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf\rnpdfJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\react\renderer\components\rnpdf

build rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o: CXX_COMPILER__react_codegen_rnpdf_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/rnpdf-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpdf
  DEP_FILE = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir\rnpdf-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/react/renderer/components/rnpdf -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir
  OBJECT_FILE_DIR = rnpdf_autolinked_build\CMakeFiles\react_codegen_rnpdf.dir



#############################################
# Object library react_codegen_rnpdf

build rnpdf_autolinked_build/react_codegen_rnpdf: phony rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnpdf_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnpdf_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnpdf_autolinked_build/edit_cache: phony rnpdf_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnpdf_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnpdf_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnpdf_autolinked_build/rebuild_cache: phony rnpdf_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08cc30e6d34a6d888b8a510138a1773f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\08cc30e6d34a6d888b8a510138a1773f\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\08cc30e6d34a6d888b8a510138a1773f\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/df82714b468a48ff8d267a8a6687ffa2/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\df82714b468a48ff8d267a8a6687ffa2\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\df82714b468a48ff8d267a8a6687ffa2\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/817e86f23996ff8925edbe49760832b4/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\817e86f23996ff8925edbe49760832b4\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\817e86f23996ff8925edbe49760832b4\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\02079a8fd6563793a01f7f2031e03843\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\02079a8fd6563793a01f7f2031e03843\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f953c5f106d813e098f92510f7df467c\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f953c5f106d813e098f92510f7df467c\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\02079a8fd6563793a01f7f2031e03843\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\02079a8fd6563793a01f7f2031e03843\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f953c5f106d813e098f92510f7df467c\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f953c5f106d813e098f92510f7df467c\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/60c4535508bf1017267b2ed21afe2888/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\60c4535508bf1017267b2ed21afe2888\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\60c4535508bf1017267b2ed21afe2888\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c33eea9777578878db146f213d4b85ec/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c33eea9777578878db146f213d4b85ec\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c33eea9777578878db146f213d4b85ec\source\codegen\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_RelWithDebInfo safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08cc30e6d34a6d888b8a510138a1773f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/df82714b468a48ff8d267a8a6687ffa2/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/817e86f23996ff8925edbe49760832b4/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/60c4535508bf1017267b2ed21afe2888/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c33eea9777578878db146f213d4b85ec/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db1f59bf5dfac53adaf0f6b4fbd6f53b/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\db1f59bf5dfac53adaf0f6b4fbd6f53b\react\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\db1f59bf5dfac53adaf0f6b4fbd6f53b\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/744c5fa8b643787fe14b94e28c0b51d5/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\744c5fa8b643787fe14b94e28c0b51d5\cpp\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\744c5fa8b643787fe14b94e28c0b51d5\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c1ec9899bd681a4ce6130127dfc7b1aa\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c1ec9899bd681a4ce6130127dfc7b1aa\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7596a92accea50cbc8a76fe5a69c4cda\renderer\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7596a92accea50cbc8a76fe5a69c4cda\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c1ec9899bd681a4ce6130127dfc7b1aa\components\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c1ec9899bd681a4ce6130127dfc7b1aa\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7596a92accea50cbc8a76fe5a69c4cda\renderer\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7596a92accea50cbc8a76fe5a69c4cda\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5da779b511b8f89b0933757895dc38a6/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5da779b511b8f89b0933757895dc38a6\common\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5da779b511b8f89b0933757895dc38a6\common\cpp\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\27e10798aac20b1399b1d2e716942739\jni\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\27e10798aac20b1399b1d2e716942739\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0277bfc3ac1676e5835f01033ba3d01f\codegen\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0277bfc3ac1676e5835f01033ba3d01f\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d4cf35001654458a63361f7def051d87\source\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d4cf35001654458a63361f7def051d87\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0277bfc3ac1676e5835f01033ba3d01f\codegen\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0277bfc3ac1676e5835f01033ba3d01f\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d4cf35001654458a63361f7def051d87\source\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d4cf35001654458a63361f7def051d87\source\codegen\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\27e10798aac20b1399b1d2e716942739\jni\react\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\27e10798aac20b1399b1d2e716942739\jni\react\renderer\components\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_rnscreens.so

build C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_RelWithDebInfo rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db1f59bf5dfac53adaf0f6b4fbd6f53b/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/744c5fa8b643787fe14b94e28c0b51d5/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5da779b511b8f89b0933757895dc38a6/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\quiz-bee-techs\android\app\build\intermediates\cxx\RelWithDebInfo\5i4l185u\obj\arm64-v8a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNVectorIconsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNVectorIconsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_RelWithDebInfo C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6c49d09eb192753345178d717e7f7cc5/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/483e65f44ee89b5f84e15be5bfd4ccf4/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNCWebViewSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a\RNCWebViewSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\quiz-bee-techs\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\Users\<USER>\quiz-bee-techs\android\app\.cxx\RelWithDebInfo\5i4l185u\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libappmodules.so

build libappmodules.so: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libappmodules.so

build libreact_codegen_RNCSlider.so: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so

build libreact_codegen_rnpicker.so: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so

build libreact_codegen_rnscreens.so: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react_codegen_RNCSlider: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_RNGoogleMobileAdsSpec: phony RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec

build react_codegen_RNHapticFeedbackSpec: phony RNHapticFeedbackSpec_autolinked_build/react_codegen_RNHapticFeedbackSpec

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rnclipboard: phony rnclipboard_autolinked_build/react_codegen_rnclipboard

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnpdf: phony rnpdf_autolinked_build/react_codegen_rnpdf

build react_codegen_rnpicker: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_safeareacontext: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a

build all: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all rnclipboard_autolinked_build/all RNCSlider_autolinked_build/all rnpicker_autolinked_build/all rngesturehandler_codegen_autolinked_build/all RNGoogleMobileAdsSpec_autolinked_build/all RNHapticFeedbackSpec_autolinked_build/all rnpdf_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNVectorIconsSpec_autolinked_build/all RNCWebViewSpec_autolinked_build/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/RNCSlider_autolinked_build

build RNCSlider_autolinked_build/all: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/RNGoogleMobileAdsSpec_autolinked_build

build RNGoogleMobileAdsSpec_autolinked_build/all: phony RNGoogleMobileAdsSpec_autolinked_build/react_codegen_RNGoogleMobileAdsSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/RNHapticFeedbackSpec_autolinked_build

build RNHapticFeedbackSpec_autolinked_build/all: phony RNHapticFeedbackSpec_autolinked_build/react_codegen_RNHapticFeedbackSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnclipboard_autolinked_build

build rnclipboard_autolinked_build/all: phony rnclipboard_autolinked_build/react_codegen_rnclipboard

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnpdf_autolinked_build

build rnpdf_autolinked_build/all: phony rnpdf_autolinked_build/react_codegen_rnpdf

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnpicker_autolinked_build

build rnpicker_autolinked_build/all: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony C$:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-pdf/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/quiz-bee-techs/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
