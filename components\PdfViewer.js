import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PdfService from '../src/services/PdfService';

const { width, height } = Dimensions.get('window');

const PdfViewer = ({ subject, type, chapter = null, title, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [isCached, setIsCached] = useState(false);

  useEffect(() => {
    checkIfCached();
  }, [subject, type, chapter]);

  const checkIfCached = async () => {
    try {
      const cached = await PdfService.isPdfCached(subject, type, chapter);
      setIsCached(cached);
    } catch (error) {
      console.error('Error checking cache:', error);
    }
  };

  const handleOpenPdf = async () => {
    setLoading(true);
    try {
      const success = await PdfService.openPdf(subject, type, chapter);
      if (!success) {
        Alert.alert('Error', 'Failed to open PDF');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to open PDF');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPdf = async () => {
    setDownloading(true);
    setDownloadProgress(0);
    
    try {
      await PdfService.downloadPdf(
        subject, 
        type, 
        chapter,
        (progress) => {
          setDownloadProgress(progress);
        }
      );
      
      setIsCached(true);
      Alert.alert('Success', 'PDF downloaded for offline viewing');
    } catch (error) {
      Alert.alert('Error', 'Failed to download PDF');
    } finally {
      setDownloading(false);
      setDownloadProgress(0);
    }
  };

  const getPdfUrl = () => {
    return PdfService.getPdfUrl(subject, type, chapter);
  };

  const isAvailable = PdfService.isPdfAvailable(subject, type, chapter);

  if (!isAvailable) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        <View style={styles.content}>
          <Ionicons name="document-text-outline" size={64} color="#ccc" />
          <Text style={styles.unavailableText}>PDF not available</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.pdfIcon}>
          <Ionicons 
            name="document-text" 
            size={64} 
            color={isCached ? "#4CAF50" : "#2196F3"} 
          />
          {isCached && (
            <View style={styles.cachedBadge}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            </View>
          )}
        </View>

        <Text style={styles.description}>
          {isCached ? 'Available offline' : 'Requires internet connection'}
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.openButton]}
            onPress={handleOpenPdf}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Ionicons name="open-outline" size={20} color="#fff" />
                <Text style={styles.buttonText}>Open PDF</Text>
              </>
            )}
          </TouchableOpacity>

          {!isCached && (
            <TouchableOpacity
              style={[styles.button, styles.downloadButton]}
              onPress={handleDownloadPdf}
              disabled={downloading}
            >
              {downloading ? (
                <View style={styles.downloadProgress}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.progressText}>
                    {Math.round(downloadProgress * 100)}%
                  </Text>
                </View>
              ) : (
                <>
                  <Ionicons name="download-outline" size={20} color="#fff" />
                  <Text style={styles.buttonText}>Download</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.urlText} numberOfLines={2}>
          {getPdfUrl()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  pdfIcon: {
    position: 'relative',
    marginBottom: 20,
  },
  cachedBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  openButton: {
    backgroundColor: '#2196F3',
  },
  downloadButton: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  downloadProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressText: {
    color: '#fff',
    fontSize: 14,
  },
  urlText: {
    fontSize: 12,
    color: '#999',
    marginTop: 20,
    textAlign: 'center',
  },
  unavailableText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
  },
});

export default PdfViewer;
