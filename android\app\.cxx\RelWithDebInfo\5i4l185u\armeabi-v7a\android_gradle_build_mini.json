{"buildFiles": ["C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-pdf\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\quiz-bee-techs\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\.cxx\\RelWithDebInfo\\5i4l185u\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\.cxx\\RelWithDebInfo\\5i4l185u\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"artifactName": "react_codegen_RNCSlider", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"artifactName": "react_codegen_RNCWebViewSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c": {"artifactName": "react_codegen_RNGoogleMobileAdsSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"artifactName": "react_codegen_RNHapticFeedbackSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnclipboard::@6385240493dfcaf22ab7": {"artifactName": "react_codegen_rnclipboard", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnpdf::@7ef4ba29a735fbf48be7": {"artifactName": "react_codegen_rnpdf", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"artifactName": "react_codegen_rnpicker", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\quiz-bee-techs\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\5i4l185u\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483e65f44ee89b5f84e15be5bfd4ccf4\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c49d09eb192753345178d717e7f7cc5\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}}}