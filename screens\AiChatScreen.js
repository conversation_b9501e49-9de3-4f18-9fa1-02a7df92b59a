// file: screens/AiChatScreen.js
import React, { useState, useCallback, useContext, useRef, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator, KeyboardAvoidingView, Platform, Alert, Share, Clipboard, Image, ImageBackground, ScrollView, Animated, Easing } from 'react-native'; // Keep ScrollView for potential future use
import * as Speech from 'expo-speech'; // Import Speech
import { ThemeContext } from './ThemeContext';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next'; // Import useTranslation
import Markdown from 'react-native-markdown-display'; // Import Markdown component
import axios from 'axios';
import i18n from './localization'; // Keep this for the instance reference if needed elsewhere
import { getAuth } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import Constants from 'expo-constants';

// Voice module availability check and conditional import
let Voice = null;
let isVoiceModuleAvailable = false;

try {
  // Check if we're in Expo Go (which doesn't support native voice recognition)
  const isExpoGo = Constants.appOwnership === 'expo';

  if (isExpoGo) {
    console.log('Running in Expo Go - Voice recognition not available');
    isVoiceModuleAvailable = false;
    // Create a mock Voice object to prevent errors
    Voice = {
      isAvailable: () => Promise.resolve(false),
      start: () => Promise.reject(new Error('Voice not available in Expo Go')),
      stop: () => Promise.resolve(),
      destroy: () => Promise.resolve(),
      removeAllListeners: () => {},
      onSpeechStart: null,
      onSpeechEnd: null,
      onSpeechError: null,
      onSpeechResults: null,
      onSpeechPartialResults: null
    };
  } else {
    // Only import Voice module if not in Expo Go
    Voice = require('@react-native-voice/voice').default;
    isVoiceModuleAvailable = Voice &&
      typeof Voice.isAvailable === 'function' &&
      typeof Voice.start === 'function' &&
      typeof Voice.stop === 'function';
  }
} catch (error) {
  console.warn('Voice module initialization check failed:', error);
  isVoiceModuleAvailable = false;
  // Create a mock Voice object to prevent errors
  Voice = {
    isAvailable: () => Promise.resolve(false),
    start: () => Promise.reject(new Error('Voice module not available')),
    stop: () => Promise.resolve(),
    destroy: () => Promise.resolve(),
    removeAllListeners: () => {},
    onSpeechStart: null,
    onSpeechEnd: null,
    onSpeechError: null,
    onSpeechResults: null,
    onSpeechPartialResults: null
  };
}

// Firebase Function URL for chat (without translation)
const CHAT_FUNCTION_URL = 'https://chatwithgroq-2asc2ekt6a-uc.a.run.app';

// ChatMessageItem component removed - No longer needed as renderItem is inline

const AiChatScreen = () => {
  const { t, i18n: i18nInstance } = useTranslation(); // Get t function and i18n instance
  const { darkMode, fontSizeMultiplier } = useContext(ThemeContext);
  // Removed selectedSubject state
  const [messages, setMessages] = useState([
    {
      id: 'initial-ai-msg',
      text: "Hello! i am nijeta How can i help you with your NEET preparation today?",
      sender: 'ai',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAiTyping, setIsAiTyping] = useState(false);
  const [userPhotoURL, setUserPhotoURL] = useState(null);
  const [isSpeaking, setIsSpeaking] = useState(false); // State for TTS status
  const [speakingMessageId, setSpeakingMessageId] = useState(null); // Track which message is speaking
  const [lastUserPromptForRegen, setLastUserPromptForRegen] = useState(null); // Track last user message for regen
  const [isListening, setIsListening] = useState(false); // State for voice input status
  const [voiceError, setVoiceError] = useState(''); // State for voice input error
  const [messageFeedback, setMessageFeedback] = useState({}); // State to store feedback for messages { messageId: 'up' | 'down' | null }
  const [partialResult, setPartialResult] = useState(''); // For live partial speech
  const micPulseAnim = useRef(new Animated.Value(1)).current; // For mic pulse
  const navigation = useNavigation();
  const flatListRef = useRef();

  // Removed target language logic

  // Function to load chat history from AsyncStorage
  const loadChatHistory = async () => {
    try {
      const history = await AsyncStorage.getItem('chatHistory');
      if (history) {
        setMessages(JSON.parse(history));
      }
    } catch (error) {
      console.error("Failed to load chat history", error);
    }
  };

  // Function to save chat history to AsyncStorage
  const saveChatHistory = async (currentMessages) => {
    try {
      await AsyncStorage.setItem('chatHistory', JSON.stringify(currentMessages));
    } catch (error) {
      console.error("Failed to save chat history", error);
    }
  };

  // Load chat history on component mount and save on unmount/message update
  useEffect(() => {
    loadChatHistory(); // Load history when component mounts

    const auth = getAuth();
    const currentUser = auth.currentUser;
    if (currentUser && currentUser.photoURL) {
      setUserPhotoURL(currentUser.photoURL);
    }

    // Cleanup function to stop speech when the component unmounts
    return () => {
      try {
        if (Speech && Speech.stop) {
          Speech.stop();
        }
        setIsSpeaking(false);
        setSpeakingMessageId(null);
        // Clean up Voice listeners
        if (Voice && Voice.destroy) {
          Voice.destroy().then(() => {
            if (Voice.removeAllListeners) {
              Voice.removeAllListeners();
            }
          }).catch(console.error);
        }
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    };
   }, []);

   // --- Voice Input Setup ---
   useEffect(() => {
     // Check if Voice module is available before setting up listeners
     if (!isVoiceModuleAvailable || !Voice) {
       console.log('Voice module is not available - skipping voice setup');
       return;
     }

     // Only proceed if we're not in Expo Go
     if (Constants.appOwnership === 'expo') {
       console.log('Skipping voice setup in Expo Go');
       return;
     }

     // Define event handlers
     const onSpeechStart = (e) => {
       console.log('onSpeechStart: ', e);
       setIsListening(true);
       setVoiceError('');
     };
     const onSpeechEnd = (e) => {
       console.log('onSpeechEnd: ', e);
       setIsListening(false);
     };
     const onSpeechError = (e) => {
       console.error('onSpeechError: ', e);
       setVoiceError(JSON.stringify(e.error));
       setIsListening(false); // Stop listening on error
     };
     const onSpeechResults = (e) => {
       console.log('onSpeechResults: ', e);
       if (e.value && e.value.length > 0) {
         setInputText(prev => prev + (prev ? ' ' : '') + e.value[0]); // Append result
       }
     };
     const onSpeechPartialResults = (e) => {
      // Update input text with partial results for better UX
      if (e.value && e.value.length > 0) {
        setPartialResult(e.value[0]); // Show partial result
        setInputText(e.value[0]); // Live update input field
      }
    };

     // Add listeners only if Voice module is available and not in Expo Go
     try {
       if (Voice && typeof Voice.onSpeechStart !== 'undefined') {
         Voice.onSpeechStart = onSpeechStart;
         Voice.onSpeechEnd = onSpeechEnd;
         Voice.onSpeechError = onSpeechError;
         Voice.onSpeechResults = onSpeechResults;
         Voice.onSpeechPartialResults = onSpeechPartialResults; // Enable partial results
       }
     } catch (error) {
       console.error('Error setting up Voice listeners:', error);
     }

     // Cleanup: remove listeners on unmount
     return () => {
       try {
         if (Voice && Voice.destroy) {
           Voice.destroy().then(() => {
             if (Voice.removeAllListeners) {
               Voice.removeAllListeners();
             }
           }).catch(console.error);
         }
       } catch (error) {
         console.error('Error in Voice cleanup:', error);
       }
     };
   }, []); // Run only once on mount

   // --- Voice Input Functions ---
   const startListening = async () => {
     setVoiceError('');
     setPartialResult('');
     setInputText(''); // Clear input field when starting voice input
     try {
       // Check if Voice module is available
       if (!isVoiceModuleAvailable) {
         throw new Error('Voice recognition module is not available on this device');
       }

       // Additional runtime check
       if (!Voice || !Voice.isAvailable || !Voice.start) {
         throw new Error('Voice recognition functions are not available');
       }

       // Check if Voice is available with proper null checking
       let isAvailable = false;
       try {
         isAvailable = await Voice.isAvailable();
       } catch (availabilityError) {
         console.error('Error checking voice availability:', availabilityError);
         throw new Error('Voice recognition service is not properly initialized. Please restart the app.');
       }

       if (!isAvailable) {
         throw new Error('Voice recognition is not available on this device');
       }

       // Determine locale based on current i18n language
       const locale = i18nInstance.language === 'ta' ? 'ta-IN' : 'en-US';
       await Voice.start(locale);
       setIsListening(true);
     } catch (e) {
       console.error('Voice recognition error:', e);
       let errorMsg = e.message || JSON.stringify(e);

       // Provide user-friendly error messages
       if (errorMsg.includes('PERMISSION') || errorMsg.includes('permission')) {
         errorMsg = 'Microphone permission denied. Please enable microphone access in your device settings.';
       } else if (errorMsg.includes('not available') || errorMsg.includes('unavailable') || errorMsg.includes('module is not available')) {
         errorMsg = 'Voice recognition is not available on this device.';
       } else if (errorMsg.includes('network') || errorMsg.includes('Network')) {
         errorMsg = 'Network error. Please check your internet connection and try again.';
       } else if (errorMsg.includes('busy') || errorMsg.includes('already')) {
         errorMsg = 'Voice recognition is already in use. Please try again.';
       } else if (errorMsg.includes('isSpeechAvailable') || errorMsg.includes('null') || errorMsg.includes('not properly initialized')) {
         errorMsg = 'Voice recognition service is not properly initialized. Please restart the app.';
       }

       setVoiceError(errorMsg);
       setIsListening(false);

       // Show alert for critical errors
       if (errorMsg.includes('permission') || errorMsg.includes('not available') || errorMsg.includes('not properly initialized')) {
         Alert.alert('Voice Recognition Error', errorMsg);
       }
     }
   };

   const stopListening = async () => {
     try {
       if (Voice && Voice.stop) {
         await Voice.stop();
       }
       setIsListening(false);
       console.log('Stopped listening');
     } catch (e) {
       console.error('Error stopping voice recognition:', e);
       setVoiceError(e.message || JSON.stringify(e));
       setIsListening(false); // Ensure we set to false even on error
     }
   };

   // Effect to update initial message if language changes after mount
   useEffect(() => {
    setMessages(prevMessages => {
      if (prevMessages.length === 1 && prevMessages[0].id === 'initial-ai-msg') {
        // Directly use the specified string, ignoring t() for this specific message
        return [{ ...prevMessages[0], text: "Hello! i am nijeta How can i help you with your NEET preparation today?" }];
      }
      // For other messages or if history is loaded, this part won't affect the initial message
      return prevMessages;
    });
  }, [i18nInstance.language]); // Rerun when language changes, removed t from dependencies

  // Save chat history whenever messages state updates
  useEffect(() => {
    saveChatHistory(messages);
  }, [messages]);

  // --- Text-to-Speech Functions ---
  // Wrap speakMessage with useCallback
  const speakMessage = useCallback(async (messageId, text) => {
    const thingToSay = String(text ?? '');
    if (!thingToSay) return;

    // Check if Speech module is available
    if (!Speech || !Speech.speak || !Speech.stop) {
      console.error('Speech module is not available');
      Alert.alert(t('speech_error_alert'), 'Text-to-speech is not available on this device.');
      return;
    }

    try {
      const currentSpeakingId = speakingMessageId;
      await Speech.stop(); // Stop any current speech

      if (isSpeaking && currentSpeakingId === messageId) {
        // If the same message's speaker icon is tapped again, just stop it
        setIsSpeaking(false);
        setSpeakingMessageId(null);
      } else {
        // Otherwise, start speaking the new message
        setSpeakingMessageId(messageId);
        setIsSpeaking(true);
        Speech.speak(thingToSay, {
          // language: 'en-US', // Optional: Specify language if needed
          onDone: () => {
            setIsSpeaking(false);
            setSpeakingMessageId(null);
          },
          onStopped: () => {
            // Ensure state is reset even if stopped manually or by error
            if (speakingMessageId === messageId) { // Only reset if this was the message being stopped
               setIsSpeaking(false);
               setSpeakingMessageId(null);
            }
          },
          onError: (error) => {
            console.error('Speech error:', error);
            setIsSpeaking(false);
            setSpeakingMessageId(null);
            Alert.alert(t('speech_error_alert'), t('could_not_play_audio_alert'));
          },
        });
      }
    } catch (error) {
      console.error('Error in speakMessage:', error);
      setIsSpeaking(false);
      setSpeakingMessageId(null);
      Alert.alert(t('speech_error_alert'), 'Could not start text-to-speech.');
    }
  }, [isSpeaking, speakingMessageId, t]); // Add dependencies

   // Stop speech explicitly (e.g., when sending a new message or clearing chat)
   const stopSpeaking = async () => {
     if (isSpeaking) {
       try {
         if (Speech && Speech.stop) {
           await Speech.stop();
         }
         setIsSpeaking(false);
         setSpeakingMessageId(null);
       } catch (error) {
         console.error('Error stopping speech:', error);
         setIsSpeaking(false);
         setSpeakingMessageId(null);
       }
     }
   };

   // --- Feedback Functions ---
   const handleFeedback = useCallback((messageId, feedbackType) => {
     setMessageFeedback(prev => ({
       ...prev,
       [messageId]: feedbackType,
     }));
     // TODO: Implement sending feedback to backend/analytics
     console.log(`Feedback for message ${messageId}: ${feedbackType}`);
   }, []); // No dependencies needed as it uses state updater function

  // --- Language Toggle Function ---
  const toggleLanguage = async () => {
    const currentLanguage = i18nInstance.language;
    const nextLanguage = currentLanguage === 'en' ? 'ta' : 'en';
    await i18nInstance.changeLanguage(nextLanguage);
    try {
      await AsyncStorage.setItem('appLanguage', nextLanguage);
    } catch (error) {
      console.error("Failed to save language preference", error);
      // Optionally show an alert to the user
    }
    // Stop speech when language changes as the voice might not match
    await stopSpeaking();
  };


  // Clear chat history function
  const clearChatHistory = async () => {
    await stopSpeaking(); // Stop speech before clearing
    try {
      await AsyncStorage.removeItem('chatHistory');
      setMessages([{
        id: 'initial-ai-msg',
        text: "Hello! i am nijeta How can i help you with your NEET preparation today?",
        sender: 'ai',
        timestamp: new Date()
      }]); // Reset messages to initial state
    } catch (error) {
      console.error("Failed to clear chat history", error);
      Alert.alert(t('clear_error_alert'), t('failed_clear_history_alert'));
    }
  };

  const _handleAiError = (error) => {
    let errorMessage = "An error occurred while contacting the AI assistant.";
    let displayMessage = errorMessage;

    if (axios.isAxiosError(error)) {
      if (error.response) {
        const status = error.response.status;
        const apiMessage = typeof error.response.data === 'string' ? error.response.data : (error.response.data?.message || 'Function execution error');
        errorMessage = `AI Function Error (${status}): ${apiMessage}`;
        if (status >= 500) {
          displayMessage = "AI Error: The AI service encountered an internal issue. Please try again later.";
        } else if (status === 400) {
           displayMessage = `AI Error: Invalid request sent to the AI service. (${apiMessage})`;
        } else {
          displayMessage = `AI Error: ${apiMessage} (Code: ${status})`;
        }
      } else if (error.request) {
        errorMessage = "AI Network Error: No response received from the function.";
        displayMessage = "Network Error: Could not connect to the AI service. Please check your internet connection.";
      } else {
        errorMessage = `Axios Setup Error: ${error.message}`;
        displayMessage = "Error: Could not send the request to the AI service.";
      }
    } else {
      errorMessage = `Unexpected Error: ${error.message}`;
      displayMessage = "An unexpected error occurred.";
    }
    console.error('AI Send Error:', errorMessage);
    return displayMessage;
  };


  const handleSend = async () => {
    await stopSpeaking(); // Stop any ongoing speech before sending
    const userMessageText = inputText.trim();
    if (userMessageText.length === 0) return;

    // Removed subject prefixing
    const fullPrompt = userMessageText;

    // Ensure consistent structure for user messages - re-added showTranslation
    const newUserMessage = {
      id: Date.now().toString(),
      text: userMessageText,
      sender: 'user',
      timestamp: new Date()
      // Removed translation fields
    };
    setMessages(prev => [...prev, newUserMessage]);
    setLastUserPromptForRegen(newUserMessage); // Store the prompt for potential regeneration
    setInputText('');
    setIsLoading(true);
    setIsAiTyping(true);

    try {
      // Use current messages state directly for conversation history
      const currentMessages = [...messages, newUserMessage];
      const conversation = currentMessages.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: String(msg.text ?? '')
      }));
      conversation.pop();
      conversation.push({ role: 'user', content: fullPrompt }); // Send original text

      // --- Call the Chat Function ---
      const requestPayload = {
        messages: conversation,
        targetLanguage: i18nInstance.language // Send current language
      };

      // Removed placeholder URL check

      const response = await axios.post(CHAT_FUNCTION_URL, requestPayload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 120000 // Increased timeout for potential translation delay
      });

      // --- Process Response from Chat Function ---
      let responseText = t('ai_response_error'); // Use translation key

      // The chatWithGroq function returns the response in { reply: { content: '...' } }
      if (response.data && response.data.reply && response.data.reply.content) {
          responseText = response.data.reply.content.trim();
      } else if (response.data && response.data.error) { // Handle potential errors reported by the function
          console.error("Error from chat function:", response.data.error);
          responseText = response.data.error || "An error occurred during AI processing.";
      } else {
          // Log unexpected structure if needed
          console.error("Unexpected response structure from chat function:", response.data);
          // Keep the default "Sorry..." message
      }


      // Ensure consistent structure for the new AI message (no translation fields)
      const newAiMessage = {
        id: (Date.now() + 1).toString(),
        text: responseText, // Use the text received
        sender: 'ai',
        timestamp: new Date()
        // Removed translation fields
      };
      setMessages(prev => [...prev, newAiMessage]);

    } catch (error) {
      const displayMessage = _handleAiError(error);
      Alert.alert(t('ai_error_alert'), displayMessage); // Use translation key for title
      // Ensure consistent structure for error messages - re-added showTranslation
      const errorAiMessage = {
        id: (Date.now() + 1).toString(),
        text: displayMessage,
        sender: 'ai',
        timestamp: new Date()
        // Removed translation fields
      };
      setMessages(prev => [...prev, errorAiMessage]);
    } finally {
      setIsLoading(false);
      setIsAiTyping(false);
    }
  };

  // --- Regenerate Function ---
  const handleRegenerate = async () => {
    if (!lastUserPromptForRegen || isLoading) return; // Don't regenerate if no prompt or already loading

    await stopSpeaking(); // Stop any ongoing speech

    // Find the index of the last AI message
    let lastAiIndex = -1;
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].sender === 'ai') {
        lastAiIndex = i;
        break;
      }
    }

    // Only proceed if there's an AI message to replace and it's the last message overall
    if (lastAiIndex !== messages.length - 1) {
      console.log("Regeneration only possible for the very last AI message.");
      // Optionally show an alert: Alert.alert("Cannot Regenerate", "You can only regenerate the last response.");
      return;
    }

    // Remove the last AI message
    const messagesBeforeRegen = messages.slice(0, lastAiIndex);
    setMessages(messagesBeforeRegen); // This updates the state, but the variable `messagesBeforeRegen` is local

    setIsLoading(true);
    setIsAiTyping(true);

    try {
      // Reconstruct conversation history up to the point *before* the regenerated message
      const conversation = messagesBeforeRegen.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: String(msg.text ?? '')
      }));
      // Ensure the last user prompt is included correctly
      if (conversation[conversation.length - 1]?.role !== 'user') {
         conversation.push({ role: 'user', content: String(lastUserPromptForRegen.text ?? '') });
      }


      // --- Call the Chat Function (same logic as handleSend) ---
      const requestPayload = {
        messages: conversation,
        targetLanguage: i18nInstance.language // Send current language
      };

      const response = await axios.post(CHAT_FUNCTION_URL, requestPayload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 120000
      });

      let responseText = t('ai_response_error'); // Use translation key
      if (response.data && response.data.reply && response.data.reply.content) {
          responseText = response.data.reply.content.trim();
      } else if (response.data && response.data.error) {
          console.error("Error from chat function:", response.data.error);
          responseText = response.data.error || "An error occurred during AI processing.";
      } else {
          console.error("Unexpected response structure from chat function:", response.data);
      }

      const newAiMessage = {
        id: (Date.now() + 1).toString(), // New ID for the regenerated message
        text: responseText,
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, newAiMessage]); // Add the new AI message

    } catch (error) {
      const displayMessage = _handleAiError(error);
      Alert.alert(t('ai_error_alert'), displayMessage);
      // Add back the original AI message or an error message? Let's add an error message.
       const errorAiMessage = {
         id: (Date.now() + 1).toString(),
         text: displayMessage,
         sender: 'ai',
         timestamp: new Date()
       };
       // Add error message back to the original message list (before regen attempt)
       setMessages(prev => [...messagesBeforeRegen, errorAiMessage]);
    } finally {
      setIsLoading(false);
      setIsAiTyping(false);
    }
  };


  // handlePromptPress function removed

  // Removed handleToggleTranslation function

  const handleClearChat = () => {
    Alert.alert(t('clear_chat_alert_title'), t('clear_chat_alert_message'), [
      { text: t('cancel_button'), style: "cancel" },
      { text: t('clear_button'), onPress: clearChatHistory, style: "destructive" }
    ]);
  };

  const handleStopGenerating = async () => {
    await stopSpeaking(); // Stop speech if user stops generation
    setIsLoading(false);
    setIsAiTyping(false);
    // Ensure consistent structure for stop messages
    const stopMsg = {
      id: (Date.now() + 1).toString(),
      text: t('generation_stopped_message'), // Use t() here
      sender: 'ai',
      timestamp: new Date()
      // Removed translation fields
    };
    setMessages(prev => [...prev, stopMsg]);
  };

  // Wrap handleCopyMessage with useCallback
  const handleCopyMessage = useCallback((text) => {
    Clipboard.setString(String(text ?? ''));
    Alert.alert(t('copied_alert'), t('message_copied_alert'));
  }, [t]); // Add t as dependency

  // Wrap handleShareMessage with useCallback
  const handleShareMessage = useCallback(async (text) => {
    try {
      await Share.share({ message: String(text ?? '') });
    } catch (error) {
      Alert.alert(t('share_error_alert'), t('could_not_share_alert'));
    }
  }, [t]); // Add t as dependency

  // Auto-scroll effect
  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      // Add a small delay to ensure layout is complete before scrolling
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]); // Trigger only when messages array changes

  // Pulse animation for mic
  useEffect(() => {
    if (isListening) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(micPulseAnim, {
            toValue: 1.3,
            duration: 500,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(micPulseAnim, {
            toValue: 1,
            duration: 500,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      micPulseAnim.setValue(1);
    }
  }, [isListening]);

  // Define props for ChatMessageItem that depend on AiChatScreen state/context
  const aiProfilePic = require('../assets/images/beetech_icon.png');
  const userProfilePicSource = userPhotoURL ? { uri: userPhotoURL } : require('../assets/images/icon.png');

  // Wrap renderItem with useCallback
  const renderItem = useCallback(({ item }) => {
    const isUser = item.sender === 'user';
    const messageStyle = isUser ? styles.userMessage : styles.aiMessage;
    const dynamicTextStyle = { fontSize: 14 * fontSizeMultiplier, color: '#FFFFFF' };
    const dynamicBubbleStyle = { backgroundColor: isUser ? 'rgba(76, 175, 80, 0.8)' : 'rgba(156, 39, 176, 0.8)' };

    // Ensure text is always a string
    const displayText = String(item.text ?? ''); // Always display the original text
    const isCurrentlySpeaking = isSpeaking && speakingMessageId === item.id;

    // Basic markdown style matching the text color
    const markdownStyle = {
      body: { color: dynamicTextStyle.color, fontSize: dynamicTextStyle.fontSize },
      heading1: { color: dynamicTextStyle.color, fontSize: dynamicTextStyle.fontSize * 1.5, fontWeight: 'bold' },
      heading2: { color: dynamicTextStyle.color, fontSize: dynamicTextStyle.fontSize * 1.3, fontWeight: 'bold' },
      bullet_list: { marginBottom: 10 },
      ordered_list: { marginBottom: 10 },
      list_item: { flexDirection: 'row', alignItems: 'center', marginBottom: 5 },
      code_inline: { backgroundColor: 'rgba(0,0,0,0.1)', padding: 2, borderRadius: 3 },
      code_block: { backgroundColor: 'rgba(0,0,0,0.1)', padding: 10, borderRadius: 3, marginVertical: 5 },
      fence: { backgroundColor: 'rgba(0,0,0,0.1)', padding: 10, borderRadius: 3, marginVertical: 5 },
    };

    return (
      <View style={[styles.messageContainer, isUser ? styles.userMessageContainer : styles.aiMessageContainer]}>
        {!isUser && <Image source={aiProfilePic} style={styles.profilePic} />}
        <View style={[styles.messageBubble, messageStyle, dynamicBubbleStyle]}>
          {/* Render display text using Markdown component */}
          {displayText.length > 0 && (
            <Markdown style={markdownStyle}>{displayText}</Markdown>
          )}

          {/* Timestamp and Actions Row */}
          <View style={styles.timestampAndActionsContainer}>
            {/* Render timestamp if it exists */}
            {item.timestamp ? (
              <Text style={[styles.timestampText, { fontSize: 10 * fontSizeMultiplier, color: styles.BORDER_COLOR_LIGHT }]}>
                {`${new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`}
              </Text>
            ) : (
              <View /> // Placeholder for spacing
            )}

            <View style={styles.messageActions}>
              {/* Speaker Button for AI messages */}
              {!isUser && (
                <TouchableOpacity
                  onPress={() => speakMessage(item.id, displayText)}
                  style={styles.actionButton}
                  accessibilityLabel={t('speak_message_button_label')}
                >
                  <Ionicons
                    name={isCurrentlySpeaking ? "volume-high" : "volume-medium-outline"}
                    size={18}
                    color={isCurrentlySpeaking ? '#00FF00' : '#FFFFFF'} // Green when speaking
                  />
                </TouchableOpacity>
              )}

              {/* Copy and Share buttons (acting on displayed text) */}
              <TouchableOpacity
                onPress={() => handleCopyMessage(displayText)}
                style={styles.actionButton}
                accessibilityLabel={t('copy_message_button_label')}
              >
                <Ionicons name="copy-outline" size={18} color={'#FFFFFF'} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleShareMessage(displayText)}
                style={styles.actionButton}
                accessibilityLabel={t('share_message_button_label')}
              >
                <Ionicons name="share-social-outline" size={18} color={'#FFFFFF'} />
             </TouchableOpacity>
           </View>
         </View>

         {/* Feedback Buttons (only for AI messages) */}
         {!isUser && (
           <View style={styles.feedbackContainer}>
             <TouchableOpacity
               onPress={() => handleFeedback(item.id, 'up')}
               style={styles.feedbackButton}
               accessibilityLabel={t('thumbs_up_button_label')}
             >
               <Ionicons
                 name={messageFeedback[item.id] === 'up' ? "thumbs-up" : "thumbs-up-outline"}
                 size={18}
                 color={messageFeedback[item.id] === 'up' ? '#00FF00' : '#FFFFFF'} // Green when selected
               />
             </TouchableOpacity>
             <TouchableOpacity
               onPress={() => handleFeedback(item.id, 'down')}
               style={styles.feedbackButton}
               accessibilityLabel={t('thumbs_down_button_label')}
             >
               <Ionicons
                 name={messageFeedback[item.id] === 'down' ? "thumbs-down" : "thumbs-down-outline"}
                 size={18}
                 color={messageFeedback[item.id] === 'down' ? 'red' : '#FFFFFF'} // Red when selected
               />
             </TouchableOpacity>
           </View>
         )}
       </View>
         {isUser && <Image source={userProfilePicSource} style={styles.profilePic} />}
       </View>
     );
   }, [fontSizeMultiplier, userPhotoURL, aiProfilePic, userProfilePicSource, isSpeaking, speakingMessageId, speakMessage, handleCopyMessage, handleShareMessage, t, handleFeedback, messageFeedback]); // Add dependencies and new state/function

  return (
    <ImageBackground
      source={require('../assets/images/background.jpg')}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
        keyboardVerticalOffset={Platform.OS === "ios" ? 60 : 0}
      >
        <View style={styles.header}>
          {/* Profile Picture */}
          <Image
            source={userPhotoURL ? { uri: userPhotoURL } : require('../assets/images/beetech_icon.png')}
            style={styles.headerProfilePic}
          />
          <Text style={styles.headerTitle}>{t('ai_assistant_title')}</Text>
          <View style={styles.headerActions}>
           <TouchableOpacity
             onPress={toggleLanguage}
             style={styles.languageButton}
             accessibilityLabel={t('translate_button_label')}
            >
             <MaterialCommunityIcons name="translate" size={24} color={TEXT_COLOR_WHITE} />
           </TouchableOpacity>
           <TouchableOpacity
             onPress={handleClearChat}
             style={styles.clearButton}
             accessibilityLabel={t('clear_chat_button_label')}
            >
             <MaterialCommunityIcons name="delete-sweep-outline" size={24} color={TEXT_COLOR_WHITE} />
           </TouchableOpacity>
          </View>
        </View>

        {/* Subject Selector Removed */}
        {/* <View style={styles.subjectSelectorContainer}> ... </View> */}

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderItem} // Use direct renderItem
          keyExtractor={(item) => item.id}
          style={styles.listContainer}
          contentContainerStyle={{ paddingBottom: 10 }}
          extraData={speakingMessageId} // Re-render list when speaking state changes
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
        />

        {/* Regenerate Button - Show only if last message is AI and not loading */}
         {messages.length > 1 && messages[messages.length - 1].sender === 'ai' && !isLoading && !isAiTyping && lastUserPromptForRegen && (
           <View style={styles.regenerateButtonContainer}>
             <TouchableOpacity
               onPress={handleRegenerate}
               style={styles.regenerateButton}
               accessibilityLabel={t('regenerate_button_label')}
              >
               <MaterialCommunityIcons name="refresh" size={18} color={TEXT_COLOR_WHITE} />
               {/* Optionally add text: <Text style={styles.regenerateButtonText}>Regenerate</Text> */}
             </TouchableOpacity>
          </View>
        )}

        {isAiTyping && (
          <View>
            <View style={styles.typingIndicatorContainer}>
              <Text style={[styles.typingIndicatorText, {fontSize: 12 * fontSizeMultiplier, color: styles.TEXT_COLOR_WHITE}]}>{t('typing_indicator')}</Text>
              <ActivityIndicator size="small" color={styles.TEXT_COLOR_WHITE} />
            </View>
          </View>
        )}

         {isLoading && !isAiTyping && ( // Show stop button only when loading but not typing (i.e., waiting for response)
           <View>
             <View style={styles.stopButtonContainer}>
               <TouchableOpacity
                 onPress={handleStopGenerating}
                 style={styles.stopButton}
                 accessibilityLabel={t('stop_generating_button_label')}
                >
                 <MaterialCommunityIcons name="stop-circle-outline" size={18} color={TEXT_COLOR_WHITE} />
                 <Text style={styles.stopButtonText}>{t('stop_generating_button')}</Text>
               </TouchableOpacity>
            </View>
          </View>
        )}

         {/* Prompt Suggestions Area Removed */}
         {/* <View style={styles.promptContainer}> ... </View> */}

         {/* Display Voice Error if any */}
         {voiceError ? <Text style={styles.errorText}>{`Voice Error: ${voiceError}`}</Text> : null}
         {/* Listening indicator */}
         {isListening && (
           <View style={styles.listeningIndicatorContainer}>
             <Text style={styles.listeningIndicatorText}>Listening...</Text>
             <ActivityIndicator size="small" color={TEXT_COLOR_WHITE} />
           </View>
         )}
         <View style={styles.inputContainer}>
           {/* Microphone Button with pulse animation - only show if Voice is available */}
           {isVoiceModuleAvailable && (
             <Animated.View style={{ transform: [{ scale: isListening ? micPulseAnim : 1 }] }}>
               <TouchableOpacity
                 onPress={isListening ? stopListening : startListening}
                 style={styles.micButton}
                 accessibilityLabel={t('mic_button_label')}
               >
                 <MaterialCommunityIcons
                   name={isListening ? "microphone-off" : "microphone"}
                   size={24}
                   color={isListening ? 'red' : TEXT_COLOR_WHITE}
                 />
               </TouchableOpacity>
             </Animated.View>
           )}
           <TextInput
             style={styles.input}
             value={inputText}
             onChangeText={setInputText}
             placeholder={t('ask_anything_placeholder')}
             placeholderTextColor={PLACEHOLDER_TEXT_COLOR}
             multiline
           />
           <TouchableOpacity
             onPress={handleSend}
             style={styles.sendButton}
             disabled={isLoading || inputText.trim().length === 0}
             accessibilityLabel={t('send_button_label')}
            >
             {isLoading ? (
               <ActivityIndicator size="small" color={TEXT_COLOR_WHITE} />
            ) : (
              <Ionicons name="send" size={20} color={styles.TEXT_COLOR_WHITE} />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
};

// Style Constants - Defined outside StyleSheet.create
const BACKGROUND_COLOR_DARK = 'rgba(30, 30, 30, 0.8)';
const BACKGROUND_COLOR_LIGHT = '#F0F0F0';
const TEXT_COLOR_WHITE = '#FFFFFF';
const TEXT_COLOR_BLACK = '#000000';
const PLACEHOLDER_TEXT_COLOR = '#AAAAAA';
const USER_BUBBLE_COLOR = 'rgba(76, 175, 80, 0.8)';
const AI_BUBBLE_COLOR = 'rgba(156, 39, 176, 0.8)';
const SEND_BUTTON_COLOR = 'rgba(0, 122, 255, 0.8)';
const STOP_BUTTON_COLOR = '#DDD';
const BORDER_COLOR_DARK = 'rgba(51, 51, 51, 0.8)';
const BORDER_COLOR_LIGHT = '#E0E0E0';

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? 40 : 50,
    paddingBottom: 10,
    backgroundColor: BACKGROUND_COLOR_DARK,
    borderBottomWidth: 1,
    borderBottomColor: BORDER_COLOR_DARK,
  },
  headerTitle: {
    flex: 1, // Allow title to take available space
    textAlign: 'center', // Center title
    fontSize: 20,
    fontWeight: 'bold',
    color: TEXT_COLOR_WHITE,
    marginLeft: 10,
  },
  headerProfilePic: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 10, // Add margin to separate from title/actions
  },
  headerActions: {
     flexDirection: 'row',
     alignItems: 'center',
  },
  languageButton: {
     padding: 5,
     marginRight: 10, // Space between language and clear buttons
  },
  clearButton: {
    padding: 5,
  },
  listContainer: {
    flex: 1,
  },
  messageContainer: {
    marginVertical: 5,
    marginHorizontal: 10,
    flexDirection: 'row',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
  },
  profilePic: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginHorizontal: 8,
    alignSelf: 'flex-end',
  },
  messageBubble: {
    maxWidth: '75%',
    padding: 10,
    borderRadius: 15,
    elevation: 2, // Android shadow
    shadowOffset: { width: 1, height: 1 }, // iOS shadow
    shadowColor: '#333', // iOS shadow
    shadowOpacity: 0.3, // iOS shadow
    shadowRadius: 2, // iOS shadow
  },
  userMessage: {
    borderBottomRightRadius: 10,
    borderTopRightRadius: 10,
  },
  aiMessage: {
    borderBottomLeftRadius: 10,
    borderTopLeftRadius: 10,
  },
  // translatedTextStyle style removed as it's no longer used
  timestampText: {
    marginLeft: 0,
    marginRight: 5,
    color: BORDER_COLOR_LIGHT,
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center', // Align icons vertically
  },
   actionButton: {
     marginLeft: 10,
     padding: 3,
   },
   micButton: {
     padding: 10, // Make it easier to press
     marginRight: 5,
     justifyContent: 'center', // Center icon vertically
   },
   inputContainer: {
     flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: BORDER_COLOR_DARK,
    backgroundColor: 'rgba(40, 40, 40, 0.8)', // Slightly lighter background
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 120,
    backgroundColor: '#F0F0F0', // Assuming light mode default for input background
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    fontSize: 16,
    color: '#000', // Changed input text color to black
  },
  sendButton: {
    backgroundColor: SEND_BUTTON_COLOR,
    borderRadius: 20,
    width: 50, // Increased width
    height: 50, // Increased height
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButtonContainer: {
    alignItems: 'center',
    paddingVertical: 5,
  },
  stopButton: {
    backgroundColor: 'rgba(220, 53, 69, 0.8)', // Red color
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  stopButtonText: {
    color: TEXT_COLOR_WHITE, // Changed stop button text to white for better contrast
    marginLeft: 5,
    fontSize: 14,
  },
  regenerateButtonContainer: {
    alignItems: 'center',
    paddingVertical: 5,
  },
  regenerateButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.7)', // Similar to send button, slightly transparent
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Optional style for regenerate text if added
  // regenerateButtonText: {
  //   color: TEXT_COLOR_WHITE,
  //   marginLeft: 5,
  //   fontSize: 14,
  // },
  loadingIndicator: {
    marginVertical: 10,
  },
  typingIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  typingIndicatorText: {
    marginRight: 8,
    fontStyle: 'italic',
    color: TEXT_COLOR_WHITE, // Changed typing indicator text to white
  },
  // Styles for subject selector removed
  // subjectSelectorContainer: { ... },
  // subjectButton: { ... },
  // selectedSubjectButton: { ... },
  // subjectButtonText: { ... },
  timestampAndActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
     marginTop: 5,
   },
   errorText: { // Style for voice error message
     color: 'red',
     textAlign: 'center',
     marginBottom: 5,
     marginHorizontal: 15, // Add some horizontal margin
   },
   listeningIndicatorContainer: { // Style for listening indicator container
     flexDirection: 'row',
     alignItems: 'center',
     justifyContent: 'center',
     paddingVertical: 5,
   },
   listeningIndicatorText: { // Style for listening indicator text
     color: TEXT_COLOR_WHITE,
     marginRight: 8,
     fontStyle: 'italic',
   },
   feedbackContainer: { // Style for feedback buttons container
     flexDirection: 'row',
     marginTop: 5,
     alignSelf: 'flex-end', // Align to the right within the AI bubble
   },
   feedbackButton: { // Style for individual feedback buttons
     padding: 5,
     marginLeft: 10,
   },
   // Styles for prompt suggestions removed
   // promptContainer: { ... },
   // promptButton: { ... },
   // promptButtonText: { ... },
 });

 export default AiChatScreen;
