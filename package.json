{"license": "0BSD", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint ."}, "dependencies": {"@emotion/is-prop-valid": "*", "@expo-google-fonts/poppins": "^0.3.0", "@expo/config-plugins": "~9.0.0", "@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/slider": "4.5.5", "@react-native-picker/picker": "2.9.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "*", "@react-navigation/drawer": "*", "@react-navigation/native": "*", "@react-navigation/stack": "*", "abort-controller": "^3.0.0", "axios": "^1.8.4", "eslint-config-react-app": "^7.0.1", "expo": "~52.0.42", "expo-app-loading": "*", "expo-asset": "~11.0.5", "expo-av": "~15.0.2", "expo-build-properties": "~0.13.3", "expo-clipboard": "^7.0.1", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-file-system": "^18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-notifications": "~0.29.14", "expo-sharing": "~13.0.1", "expo-speech": "^13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "firebase": "^11.6.0", "form-data": "^4.0.2", "framer-motion": "^11.2.10", "i18next": "^23.2.3", "intl-pluralrules": "^2.0.1", "lodash": "*", "moti": "^0.29.0", "react": "18.3.1", "react-i18next": "*", "react-native": "0.76.9", "react-native-chart-kit": "^6.12.0", "react-native-fast-image": "*", "react-native-gesture-handler": "~2.20.2", "react-native-google-mobile-ads": "^14.11.0", "react-native-haptic-feedback": "*", "react-native-markdown-display": "^7.0.2", "react-native-paper": "4.9.2", "react-native-pdf": "^6.7.7", "react-native-picker-select": "*", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-vector-icons": "*", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.20.0", "eslint": "^8.57.1", "eslint-config-expo": "^8.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0"}, "overrides": {"framer-motion": "^11.2.10"}, "private": true, "name": "quiz-bee-techs", "version": "1.3.0", "description": "Open the `App.js` file to start writing some code. You can preview the changes directly on your phone or tablet by scanning the **QR code** or use the iOS or Android emulators. When you're done, click **Save** and share the link!", "keywords": [], "author": "", "type": "commonjs"}