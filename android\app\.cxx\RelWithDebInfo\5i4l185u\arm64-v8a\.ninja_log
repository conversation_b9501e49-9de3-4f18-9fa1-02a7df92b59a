# ninja log v5
128936	161684	7704934855765719	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08cc30e6d34a6d888b8a510138a1773f/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	23621c24a9c4dbb1
135385	177069	7704934934448276	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	a0fcd96b004288d
6	101	0	C:/Users/<USER>/quiz-bee-techs/android/app/.cxx/RelWithDebInfo/5i4l185u/arm64-v8a/CMakeFiles/cmake.verify_globs	d6bee461355cdafc
172	21685	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	abea6e50a4aec9f8
28388	55342	7704933750025372	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp.o	34056921de542b9
489	34583	7704933585790696	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ShadowNodes.cpp.o	b2a3f496d9c4b676
84493	99063	7704934220147626	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o	764f15c8c9f6b7a1
28038	55420	7704933769766935	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/120405363f2fd5f7c87dd454b0a2fc40/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp.o	ae73b19cc130b17e
373	27530	7704933515988501	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/ComponentDescriptors.cpp.o	a4156b1be8167cbe
68729	98581	7704934217107936	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	1cebbbf7c78915e3
455	19381	7704933428161947	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/States.cpp.o	5a8369a669029816
316	34817	7704933586009414	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	c265f331610868a8
276	28033	7704933514209555	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	18a1e2394ad85674
392	28172	7704933522737170	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/EventEmitters.cpp.o	f73c84771398b91f
55035	77731	7704934005235767	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	9a0a4964b8b4f17e
40515	79389	7704934014596927	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	9f2d10a05f63630d
259	19801	7704933428171959	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	3827b7ec4f908382
243	34658	7704933586944181	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	3aeddff166ffe041
572	28382	7704933523958582	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/f1826b4e1b088b69a4da731804ec55fe/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp.o	f167dc305bfa409b
28891	68477	7704933913199576	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	a1a1cb74b6a4120c
542	37860	7704933619958367	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/db5badcd17e3d6b3dc2e1b877991a66a/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp.o	d32247ac14b41e34
103920	135382	7704934594450533	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ComponentDescriptors.cpp.o	1c388a37fb9c745d
335	29069	7704933531810471	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	ec7fb4a4d959bd91
354	40404	7704933643467866	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	7d3752b97d86887f
511	28997	7704933531659326	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/rnclipboardJSI-generated.cpp.o	43dc732327c82b80
182	35570	7704933597340313	CMakeFiles/appmodules.dir/OnLoad.cpp.o	ac65fe419562e141
293	28889	7704933530349671	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	3dbdfd42492324d2
411	28802	7704933529477743	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/react/renderer/components/rnclipboard/Props.cpp.o	460c5439787b2e4
35572	81476	7704934051780870	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	56edbc60c7adbd5a
28806	55030	7704933750015077	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp.o	b1d44ab3a3d2705d
34585	84125	7704934082914153	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	c09564252c42bc68
28175	55196	7704933750015077	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp.o	6b297120b77f7e85
19804	55538	7704933771149618	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c68754e0d236c318d9d47d16f84798b0/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp.o	17486a87465b20f6
155599	185401	7704935096965598	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	4dbfb6de4707eca
19404	54831	7704933769641618	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/47050d7efd95dbd227bb716bea95769f/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp.o	bce3bef26bef0f64
34660	55616	7704933771340493	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/e64e4c8e457947268e1502aa0d45cea1/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	dad780a874b9576f
431	40513	7704933645512898	rnclipboard_autolinked_build/CMakeFiles/react_codegen_rnclipboard.dir/rnclipboard-generated.cpp.o	f9f400de2f3ac38d
84158	111594	7704934354773818	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o	62dfeeb8ac75cbf3
28999	68725	7704933916348239	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	5ee13c8e0870f437
91586	131740	7704934544655439	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o	1f56f94c71a92095
34819	79709	7704934020842156	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	51288ba782044c42
54843	98917	7704934206204047	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	d1b47475ef14901e
107873	135261	7704934593715944	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/States.cpp.o	8ab5ec88344bc6d1
29071	69448	7704933932767223	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	41c311c93f04d1c3
68492	91583	7704934157352026	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	232e7b273dda441b
79429	99149	7704934228378190	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/2e26a5ddee9cdd09e5c46bc2607ebc87/RNGoogleMobileAdsSpecJSI-generated.cpp.o	c287e2fa332474af
161	279216	7704936023051169	CMakeFiles/appmodules.dir/C_/Users/<USER>/quiz-bee-techs/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	3589f6abac151b03
27538	81063	7704934051516362	RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/a868b8d5466bc67761de466f3f5bec2a/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp.o	b64b2b43ee6acfd3
146895	195631	7704935198713676	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/db1f59bf5dfac53adaf0f6b4fbd6f53b/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	6178a92c3139693d
37864	88374	7704934115075384	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	4a889d3386c6647c
55445	79913	7704934032488789	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/db1a68c37668ee0b5b844736ce4c3d03/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	87a1aa15cafedcae
79710	120202	7704934426978236	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/Props.cpp.o	88ae61571dfe2c12
81068	87955	7704934112727251	C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_RNCSlider.so	3a095f6b48c48f4d
80054	91789	7704934157392599	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/States.cpp.o	2a14406c185fa7dd
55200	100343	7704934242220648	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	8cadaa1402bc6bd1
55769	84490	7704934086920394	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	13544f466e94d78e
111333	142179	7704934661620492	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/ShadowNodes.cpp.o	aa18e1ae21768c7c
79936	101220	7704934254149770	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/EventEmitters.cpp.o	e5d314583ddcb7e6
55353	78677	7704934005847077	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/f3e179e612d5365dd1c281c151a397be/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	c0654f26cd5d1c95
78738	103918	7704934279244575	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ShadowNodes.cpp.o	c54c25310b1e4cc5
69461	133536	7704934570253058	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	243375ad4f1653f6
88385	112673	7704934367866666	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o	2f242bb7f0891ed
111597	131809	7704934554842147	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/EventEmitters.cpp.o	c55322015e1e0e59
87957	133015	7704934570293771	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o	f341949d0e1c309b
79844	117478	7704934413438042	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.cpp.o	576372c90eab436c
149750	194417	7704935185410962	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	ca4c5b114cdc9f71
40406	79841	7704934029497747	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	f000b28938b46c1
77884	110687	7704934348250687	RNGoogleMobileAdsSpec_autolinked_build/CMakeFiles/react_codegen_RNGoogleMobileAdsSpec.dir/RNGoogleMobileAdsSpec-generated.cpp.o	ee94b20b7312ae3a
91844	111332	7704934355842735	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o	341d06821072fd26
112676	156278	7704934803138242	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/Props.cpp.o	d2ccda1fa645c3a1
101223	131656	7704934544635083	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	c941bfb30a46e592
110695	131475	7704934544619805	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/react/renderer/components/rnpdf/rnpdfJSI-generated.cpp.o	f120a9e80a090178
184213	214171	7704935384050364	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o	e0fae4f150492ebb
81504	99301	7704934232788917	RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o	b2e52d34cfb56776
98584	131251	7704934544645370	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	aaa48cdf46ecdefb
98922	107872	7704934316913291	C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnpicker.so	6452eff09f3c5754
117484	135595	7704934595503353	rnpdf_autolinked_build/CMakeFiles/react_codegen_rnpdf.dir/rnpdf-generated.cpp.o	63f6d2b39f6c5f53
99303	128812	7704934501613699	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	2dd52bf704853e3b
99065	131563	7704934549598530	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	92f0a76a3ff96c23
156287	188692	7704935126324289	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	fcb1a67cea167683
99221	122954	7704934469299654	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	6a7fc7317c627b97
122960	146814	7704934709132741	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	e01605ab18bbede
133034	149701	7704934733593444	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c33eea9777578878db146f213d4b85ec/source/codegen/jni/safeareacontext-generated.cpp.o	227e9e72898a0608
100345	133402	7704934570955638	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	a05f6d7279ec826a
131744	143949	7704934676358677	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/States.cpp.o	d15971c990cf1b99
131810	148079	7704934716759357	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/60c4535508bf1017267b2ed21afe2888/safeareacontext/safeareacontextJSI-generated.cpp.o	1386c1761121b452
131480	150594	7704934730792555	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/EventEmitters.cpp.o	262873c5993c6841
176686	187555	7704935115654714	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	1e8d617a1285b47
182248	202658	7704935258588707	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	ca804bba448bc25b
131253	154119	7704934770078116	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/df82714b468a48ff8d267a8a6687ffa2/components/safeareacontext/RNCSafeAreaViewState.cpp.o	3cdec7aff0cbbab9
176929	192873	7704935166725813	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	b5fa571afee4edec
131566	154321	7704934776024450	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/02079a8fd6563793a01f7f2031e03843/renderer/components/safeareacontext/ShadowNodes.cpp.o	daf0c23d7077d5b5
131659	155597	7704934794744642	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f953c5f106d813e098f92510f7df467c/react/renderer/components/safeareacontext/Props.cpp.o	6bdf83b08137edd2
120211	163602	7704934872725762	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/817e86f23996ff8925edbe49760832b4/components/safeareacontext/ComponentDescriptors.cpp.o	14106d5baf1ab936
176775	207759	7704935318590315	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	255be1e189e81b1
163727	185592	7704935089141887	C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_safeareacontext.so	28f4624eb6bf697a
133540	148584	7704934720736196	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	e7c73c6169f0cbf2
150682	182238	7704935065109022	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	18bb6dab8c95b771
148081	176772	7704934918930510	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7596a92accea50cbc8a76fe5a69c4cda/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	d91f51c531df0e75
154123	196026	7704935200941591	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5da779b511b8f89b0933757895dc38a6/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	54027b89fc7c5418
149277	184211	7704935084528416	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c1ec9899bd681a4ce6130127dfc7b1aa/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	877d0b75202611ea
148585	197052	7704935212929385	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/744c5fa8b643787fe14b94e28c0b51d5/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	24b98a167d1c81da
135289	149275	7704934723516485	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/27e10798aac20b1399b1d2e716942739/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	dd40ff3710600c93
135726	154495	7704934783851018	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	4e8544d9b68da938
142266	195431	7704935196169267	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	47cf37a17aa86e6e
161732	196384	7704935206159464	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	f741029f01e5988a
154325	176676	7704934902411452	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o	446f55815520ab
185593	210837	7704935351051204	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o	89d62533bf807a6e
144098	201643	7704935257980871	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0277bfc3ac1676e5835f01033ba3d01f/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	f94c1cc99c1dea7
177074	205440	7704935297083431	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o	11bdbf3bf5bdc7d
154500	196137	7704935200754807	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o	e2851d683fd36afd
133407	176926	7704934908847789	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d4cf35001654458a63361f7def051d87/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	27a967cc7a8eb6e3
187556	233207	7704935574277315	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o	bf18e1dd098a0247
185405	218878	7704935430689479	RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o	2fccb6c194a7a2b4
201717	206014	7704935301638911	C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libreact_codegen_rnscreens.so	9b5235d0319d0124
21687	23340	7704985851421981	C:/Users/<USER>/quiz-bee-techs/android/app/build/intermediates/cxx/RelWithDebInfo/5i4l185u/obj/arm64-v8a/libappmodules.so	d7433b8fa7aa2930
1	182	0	clean	7899bcb4c33f8327
